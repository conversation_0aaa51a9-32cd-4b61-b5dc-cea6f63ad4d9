<?php $__env->startSection('title', 'กิจกรรม'); ?>

<?php $__env->startSection('content'); ?>
<!-- Hero Section -->
<section class="hero-section bg-primary text-white py-5">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-lg-8">
                <h1 class="display-4 fw-bold mb-3">กิจกรรมของเรา</h1>
                <p class="lead mb-4">ชมภาพบรรยากาศและกิจกรรมต่างๆ ที่เราได้จัดขึ้น เพื่อให้บริการที่ดีที่สุดแก่ครอบครัวที่สูญเสีย</p>
            </div>
            <div class="col-lg-4 text-center">
                <i class="fas fa-images fa-5x opacity-75"></i>
            </div>
        </div>
    </div>
</section>

<!-- Filter Section -->
<section class="py-4 bg-light">
    <div class="container">
        <div class="row">
            <div class="col-12">
                <div class="d-flex flex-wrap gap-2">
                    <a href="<?php echo e(route('activities.index')); ?>" 
                       class="btn <?php echo e(!request('category') ? 'btn-primary' : 'btn-outline-primary'); ?>">
                        <i class="fas fa-th me-1"></i>ทั้งหมด
                    </a>
                    <?php $__currentLoopData = $categories; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $category): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <a href="<?php echo e(route('activities.index', ['category' => $category->id])); ?>" 
                           class="btn <?php echo e(request('category') == $category->id ? 'btn-primary' : 'btn-outline-primary'); ?>"
                           style="border-color: <?php echo e($category->color); ?>; <?php echo e(request('category') == $category->id ? 'background-color: ' . $category->color . '; border-color: ' . $category->color : 'color: ' . $category->color); ?>">
                            <?php echo e($category->name); ?>

                        </a>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Activities Section -->
<section class="py-5">
    <div class="container">
        <?php if($activities->count() > 0): ?>
            <div class="row g-4">
                <?php $__currentLoopData = $activities; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $activity): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <div class="col-lg-4 col-md-6">
                    <div class="card h-100 shadow-sm">
                        <img src="<?php echo e(\App\Helpers\ImageHelper::getImageUrl($activity->cover_image)); ?>"
                             class="card-img-top"
                             alt="<?php echo e($activity->title); ?>"
                             style="height: 250px; object-fit: cover;">
                        <div class="card-body d-flex flex-column">
                            <div class="mb-2">
                                <span class="badge" style="background-color: <?php echo e($activity->category->color); ?>">
                                    <?php echo e($activity->category->name); ?>

                                </span>
                                <?php if($activity->activity_date): ?>
                                    <small class="text-muted ms-2">
                                        <i class="fas fa-calendar me-1"></i><?php echo e($activity->activity_date->format('d/m/Y')); ?>

                                    </small>
                                <?php endif; ?>
                            </div>
                            <h5 class="card-title fw-bold text-primary"><?php echo e(Str::limit($activity->title, 60)); ?></h5>
                            <p class="card-text text-muted flex-grow-1"><?php echo e(Str::limit(strip_tags($activity->description), 120)); ?></p>
                            <?php if($activity->location): ?>
                                <p class="card-text">
                                    <small class="text-muted">
                                        <i class="fas fa-map-marker-alt me-1"></i><?php echo e($activity->location); ?>

                                    </small>
                                </p>
                            <?php endif; ?>
                            <div class="mt-auto">
                                <div class="d-flex justify-content-between align-items-center">
                                    <small class="text-muted">
                                        <i class="fas fa-images me-1"></i><?php echo e($activity->images->count()); ?> รูปภาพ
                                    </small>
                                    <a href="<?php echo e(route('activities.show', $activity)); ?>" class="btn btn-primary btn-sm">
                                        <i class="fas fa-eye me-1"></i>ดูกิจกรรม
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </div>
        <?php else: ?>
            <div class="text-center py-5">
                <i class="fas fa-images fa-5x text-muted mb-4"></i>
                <h3 class="text-muted">ยังไม่มีกิจกรรม</h3>
                <p class="text-muted">เรากำลังเตรียมกิจกรรมที่น่าสนใจสำหรับคุณ</p>
            </div>
        <?php endif; ?>
    </div>
</section>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH D:\XAMPP\htdocs\SoloShop\resources\views/activities/index.blade.php ENDPATH**/ ?>