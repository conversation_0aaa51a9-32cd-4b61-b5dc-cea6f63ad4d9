<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <title>@yield('title', 'เข้าสู่ระบบ - SoloShop')</title>

    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="{{ asset('favicon.ico') }}">

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Sarabun:wght@300;400;500;600;700&family=Noto+Sans+Thai:wght@300;400;500;600;700&display=swap" rel="stylesheet">

    <style>
        :root {
            --primary-color: #2c3e50;
            --secondary-color: #34495e;
            --accent-color: #e74c3c;
            --soft-gold: #d4af37;
            --warm-white: #fdfcf8;
            --soft-gray: #7f8c8d;
            --light-gray: #ecf0f1;
            --shadow-light: rgba(0, 0, 0, 0.06);
            --shadow-medium: rgba(0, 0, 0, 0.1);
            --shadow-heavy: rgba(0, 0, 0, 0.15);
            --gradient-start: #f8f9fa;
            --gradient-end: #e9ecef;
        }

        * {
            box-sizing: border-box;
        }

        body {
            font-family: 'Noto Sans Thai', 'Sarabun', sans-serif;
            background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 2rem;
            margin: 0;
        }

        .auth-container {
            background: white;
            border-radius: 20px;
            box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15);
            overflow: hidden;
            max-width: 900px;
            width: 100%;
            display: flex;
            min-height: 500px;
            animation: gentleFloat 0.6s ease-out;
        }

        @keyframes gentleFloat {
            from {
                opacity: 0;
                transform: translateY(20px) scale(0.98);
            }
            to {
                opacity: 1;
                transform: translateY(0) scale(1);
            }
        }

        .auth-left {
            flex: 1;
            padding: 3rem;
            display: flex;
            flex-direction: column;
            justify-content: center;
            background: white;
        }

        .auth-right {
            flex: 1;
            background: linear-gradient(135deg, #e0e7ff 0%, #c7d2fe 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
            overflow: hidden;
        }

        .auth-title {
            position: absolute;
            top: 2rem;
            left: 50%;
            transform: translateX(-50%);
            color: white;
            font-size: 2rem;
            font-weight: 600;
            z-index: 10;
        }

        .login-title {
            font-size: 1.5rem;
            font-weight: 600;
            color: #1f2937;
            margin-bottom: 2rem;
            text-align: left;
        }

        .form-group {
            margin-bottom: 1.5rem;
        }

        .form-label {
            display: block;
            font-size: 0.875rem;
            font-weight: 500;
            color: #374151;
            margin-bottom: 0.5rem;
        }

        .form-control {
            border-radius: 8px;
            border: 1px solid #d1d5db;
            padding: 0.875rem 1rem;
            font-size: 0.95rem;
            transition: all 0.3s ease;
            background-color: #f9fafb;
            height: 48px;
            font-weight: 400;
            color: #374151;
            width: 100%;
        }

        .form-control:focus {
            border-color: #6366f1;
            box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
            background-color: white;
            outline: none;
        }

        .form-control::placeholder {
            color: #9ca3af;
            font-size: 0.9rem;
        }

        .form-control:not(:placeholder-shown) {
            background-color: white;
        }

        .btn-primary {
            background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
            border: none;
            border-radius: 25px;
            padding: 0.875rem 2rem;
            font-weight: 500;
            font-size: 1rem;
            transition: all 0.3s ease;
            color: white;
            width: 100%;
            height: 48px;
        }

        .btn-primary:hover {
            background: linear-gradient(135deg, #5b5fd1 0%, #7c3aed 100%);
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(99, 102, 241, 0.4);
        }

        .btn-primary:active {
            transform: translateY(0);
        }

        .form-label {
            font-weight: 400;
            color: #6c757d;
            margin-bottom: 0.5rem;
            font-size: 0.9rem;
            position: absolute;
            top: 1rem;
            left: 3.5rem;
            transition: all 0.3s ease;
            pointer-events: none;
            z-index: 1;
        }

        .form-control:focus + .form-label,
        .form-control:not(:placeholder-shown) + .form-label {
            top: -0.5rem;
            left: 1rem;
            font-size: 0.75rem;
            color: #667eea;
            background: white;
            padding: 0 0.5rem;
        }

        .auth-footer {
            text-align: center;
            padding: 2rem 2.5rem 2.5rem;
            color: var(--soft-gray);
            font-size: 0.875rem;
            background: linear-gradient(to bottom, var(--warm-white), rgba(236, 240, 241, 0.3));
        }

        .auth-footer a {
            color: var(--primary-color);
            text-decoration: none;
            font-weight: 500;
            transition: color 0.3s ease;
        }

        .auth-footer a:hover {
            color: var(--soft-gold);
            text-decoration: underline;
        }

        .logo-container {
            margin-bottom: 2rem;
            position: relative;
            z-index: 1;
        }

        .illustration {
            width: 300px;
            height: 300px;
            position: relative;
        }

        .business-person {
            width: 100%;
            height: 100%;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 400 400"><defs><linearGradient id="suit" x1="0%" y1="0%" x2="100%" y2="100%"><stop offset="0%" style="stop-color:%23374151"/><stop offset="100%" style="stop-color:%23111827"/></linearGradient><linearGradient id="shirt" x1="0%" y1="0%" x2="100%" y2="100%"><stop offset="0%" style="stop-color:%23dbeafe"/><stop offset="100%" style="stop-color:%23bfdbfe"/></linearGradient></defs><ellipse cx="200" cy="380" rx="80" ry="15" fill="rgba(0,0,0,0.1)"/><rect x="160" y="200" width="80" height="120" rx="10" fill="url(%23suit)"/><rect x="170" y="210" width="60" height="100" rx="5" fill="url(%23shirt)"/><circle cx="200" cy="150" r="35" fill="%23fbbf24"/><rect x="185" y="120" width="30" height="40" rx="15" fill="%23fbbf24"/><circle cx="190" cy="140" r="3" fill="%23374151"/><circle cx="210" cy="140" r="3" fill="%23374151"/><path d="M190 155 Q200 165 210 155" stroke="%23374151" stroke-width="2" fill="none"/><rect x="120" y="180" width="40" height="80" rx="20" fill="%23fbbf24" transform="rotate(-20 140 220)"/><rect x="240" y="180" width="40" height="80" rx="20" fill="%23fbbf24" transform="rotate(20 260 220)"/><rect x="180" y="320" width="15" height="60" fill="%23374151"/><rect x="205" y="320" width="15" height="60" fill="%23374151"/><rect x="175" y="375" width="25" height="10" rx="5" fill="%23111827"/><rect x="200" y="375" width="25" height="10" rx="5" fill="%23111827"/><rect x="250" y="160" width="60" height="40" rx="20" fill="%23fbbf24" opacity="0.8"/><rect x="260" y="170" width="40" height="20" rx="10" fill="white"/><circle cx="270" cy="175" r="3" fill="%23f59e0b"/><circle cx="280" cy="175" r="3" fill="%23f59e0b"/><circle cx="290" cy="175" r="3" fill="%23f59e0b"/></svg>') center/contain no-repeat;
        }

        .floating-elements {
            position: absolute;
            width: 100%;
            height: 100%;
            pointer-events: none;
        }

        .floating-elements::before {
            content: '';
            position: absolute;
            top: 20%;
            right: 10%;
            width: 60px;
            height: 60px;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><rect x="10" y="10" width="80" height="60" rx="5" fill="%23ffffff" stroke="%23e5e7eb" stroke-width="2"/><rect x="15" y="15" width="70" height="8" fill="%23f3f4f6"/><rect x="15" y="28" width="50" height="6" fill="%23f3f4f6"/><rect x="15" y="38" width="60" height="6" fill="%23f3f4f6"/><rect x="15" y="48" width="40" height="6" fill="%23f3f4f6"/></svg>') center/contain no-repeat;
            animation: float1 3s ease-in-out infinite;
        }

        .floating-elements::after {
            content: '';
            position: absolute;
            bottom: 30%;
            left: 15%;
            width: 40px;
            height: 40px;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="50" cy="50" r="40" fill="%23ffffff" stroke="%23e5e7eb" stroke-width="3"/><path d="M35 45 L45 55 L65 35" stroke="%236366f1" stroke-width="4" fill="none" stroke-linecap="round" stroke-linejoin="round"/></svg>') center/contain no-repeat;
            animation: float2 4s ease-in-out infinite;
        }

        @keyframes float1 {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            50% { transform: translateY(-10px) rotate(2deg); }
        }

        @keyframes float2 {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            50% { transform: translateY(-15px) rotate(-2deg); }
        }

        .alert {
            border-radius: 16px;
            margin-bottom: 2rem;
            border: none;
            animation: gentleSlideDown 0.5s ease-out;
        }

        @keyframes gentleSlideDown {
            from {
                opacity: 0;
                transform: translateY(-15px) scale(0.98);
            }
            to {
                opacity: 1;
                transform: translateY(0) scale(1);
            }
        }

        .alert-danger {
            background: linear-gradient(135deg, var(--accent-color) 0%, #c0392b 100%);
            color: var(--warm-white);
            box-shadow: 0 8px 25px rgba(231, 76, 60, 0.25);
        }

        .alert-light {
            background: rgba(255, 255, 255, 0.9);
            border: 1px solid rgba(236, 240, 241, 0.8);
            color: var(--soft-gray);
            backdrop-filter: blur(10px);
        }

        .invalid-feedback {
            font-size: 0.875rem;
            margin-top: 0.4rem;
            color: var(--accent-color);
            font-weight: 500;
        }

        .form-check {
            margin: 2rem 0;
        }

        .form-check-input {
            width: 1.3rem;
            height: 1.3rem;
            border-radius: 8px;
            border: 2px solid var(--light-gray);
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .form-check-input:checked {
            background-color: var(--soft-gold);
            border-color: var(--soft-gold);
            box-shadow: 0 4px 12px rgba(243, 156, 18, 0.3);
        }

        .form-check-label {
            color: var(--primary-color);
            font-size: 0.95rem;
            font-weight: 500;
            margin-left: 0.6rem;
            cursor: pointer;
        }

        .input-icon {
            position: absolute;
            left: 1rem;
            top: 50%;
            transform: translateY(-50%);
            color: #6c757d;
            font-size: 1.1rem;
            z-index: 2;
            transition: color 0.3s ease;
        }

        .form-control:focus ~ .input-icon {
            color: #667eea;
        }

        .password-toggle {
            position: absolute;
            right: 1.2rem;
            top: 50%;
            transform: translateY(-50%);
            background: none;
            border: none;
            color: var(--soft-gray);
            cursor: pointer;
            z-index: 2;
            transition: color 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            padding: 0.5rem;
            border-radius: 8px;
        }

        .password-toggle:hover {
            color: var(--soft-gold);
            background: rgba(243, 156, 18, 0.1);
        }

        .btn-lg {
            padding: 1.2rem 2.5rem;
            font-size: 1.1rem;
            min-height: 64px;
        }

        .forgot-password {
            color: var(--primary-color);
            text-decoration: none;
            font-weight: 500;
            font-size: 0.95rem;
            transition: all 0.3s ease;
        }

        .forgot-password:hover {
            color: var(--soft-gold);
            text-decoration: underline;
        }

        .test-credentials {
            background: #f8f9fa;
            border-radius: 12px;
            padding: 1.2rem;
            margin-top: 1.5rem;
            border: 1px solid #e9ecef;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .test-credentials:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1);
            background: #ffffff;
        }

        .test-credentials .badge {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            font-size: 0.75rem;
            padding: 0.3rem 0.6rem;
            border-radius: 6px;
        }

        @media (max-width: 768px) {
            body {
                padding: 1rem;
            }

            .auth-container {
                flex-direction: column;
                max-width: 100%;
                min-height: auto;
            }

            .auth-right {
                order: -1;
                min-height: 200px;
            }

            .auth-left {
                padding: 2rem 1.5rem;
            }

            .illustration {
                width: 200px;
                height: 200px;
            }

            .auth-title {
                font-size: 1.5rem;
                top: 1rem;
            }
        }

        /* Loading animation */
        .btn-loading {
            position: relative;
            color: transparent !important;
        }

        .btn-loading::after {
            content: '';
            position: absolute;
            width: 22px;
            height: 22px;
            top: 50%;
            left: 50%;
            margin-left: -11px;
            margin-top: -11px;
            border: 2px solid transparent;
            border-top-color: var(--warm-white);
            border-radius: 50%;
            animation: gentleSpin 1.2s linear infinite;
        }

        @keyframes gentleSpin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* Smooth transitions for all interactive elements */
        * {
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
        }

        /* Focus outline for accessibility */
        .form-control:focus,
        .btn:focus,
        .form-check-input:focus {
            outline: 2px solid var(--soft-gold);
            outline-offset: 2px;
        }

        /* Back button styles */
        .back-button {
            position: fixed;
            top: 2rem;
            left: 2rem;
            background: rgba(255, 255, 255, 0.9);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 12px;
            padding: 0.75rem 1.25rem;
            color: #374151;
            text-decoration: none;
            font-size: 0.9rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            gap: 0.5rem;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
            z-index: 1000;
        }

        .back-button:hover {
            background: rgba(255, 255, 255, 1);
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
            color: #374151;
            text-decoration: none;
        }

        .back-button i {
            font-size: 1rem;
        }

        @media (max-width: 768px) {
            .back-button {
                top: 1rem;
                left: 1rem;
                padding: 0.6rem 1rem;
                font-size: 0.85rem;
            }
        }
    </style>

    @stack('styles')
</head>
<body>
    <!-- Back button -->
    <a href="{{ route('home') }}" class="back-button">
        <i class="fas fa-arrow-left"></i>
        ย้อนกลับ
    </a>

    <div class="auth-title">Login</div>
    <div class="auth-container">
        <div class="auth-left">
            <h2 class="login-title">ลงชื่อเข้าใช้งาน</h2>
            @yield('content')
        </div>
        <div class="auth-right">
            <div class="illustration">
                <div class="business-person"></div>
                <div class="floating-elements"></div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <script>
        // Enhanced floating label functionality with smooth animations
        document.addEventListener('DOMContentLoaded', function() {
            const formControls = document.querySelectorAll('.form-control');

            formControls.forEach(function(input) {
                // Check if input has value on page load
                checkInputValue(input);

                // Add event listeners
                input.addEventListener('input', function() {
                    checkInputValue(this);
                });

                input.addEventListener('focus', function() {
                    this.parentElement.classList.add('focused');
                    addFocusEffect(this);
                });

                input.addEventListener('blur', function() {
                    this.parentElement.classList.remove('focused');
                    checkInputValue(this);
                });
            });

            function checkInputValue(input) {
                const label = input.nextElementSibling;
                if (input.value.trim() !== '' || input === document.activeElement) {
                    if (label && label.classList.contains('form-label')) {
                        label.style.top = '-0.6rem';
                        label.style.left = '1.2rem';
                        label.style.fontSize = '0.8rem';
                        label.style.color = 'var(--soft-gold)';
                        label.style.background = 'var(--warm-white)';
                        label.style.padding = '0 0.6rem';
                    }
                } else {
                    if (label && label.classList.contains('form-label')) {
                        label.style.top = '1.2rem';
                        label.style.left = '3.8rem';
                        label.style.fontSize = '0.95rem';
                        label.style.color = 'var(--soft-gray)';
                        label.style.background = 'transparent';
                        label.style.padding = '0';
                    }
                }
            }

            function addFocusEffect(input) {
                // Add subtle ripple effect
                const ripple = document.createElement('div');
                ripple.style.position = 'absolute';
                ripple.style.borderRadius = '50%';
                ripple.style.background = 'rgba(243, 156, 18, 0.3)';
                ripple.style.transform = 'scale(0)';
                ripple.style.animation = 'ripple 0.6s linear';
                ripple.style.left = '50%';
                ripple.style.top = '50%';
                ripple.style.width = '20px';
                ripple.style.height = '20px';
                ripple.style.marginLeft = '-10px';
                ripple.style.marginTop = '-10px';

                input.parentElement.style.position = 'relative';
                input.parentElement.appendChild(ripple);

                setTimeout(() => {
                    ripple.remove();
                }, 600);
            }

            // Add smooth page entrance
            document.body.style.opacity = '0';
            setTimeout(function() {
                document.body.style.transition = 'opacity 0.5s ease';
                document.body.style.opacity = '1';
            }, 150);
        });

        // Enhanced keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Enter') {
                const focusedElement = document.activeElement;
                if (focusedElement.type === 'email') {
                    document.getElementById('password').focus();
                    e.preventDefault();
                } else if (focusedElement.type === 'password') {
                    document.getElementById('loginBtn').click();
                    e.preventDefault();
                }
            }
        });

        // Add CSS for ripple animation
        const style = document.createElement('style');
        style.textContent = `
            @keyframes ripple {
                to {
                    transform: scale(4);
                    opacity: 0;
                }
            }
        `;
        document.head.appendChild(style);
    </script>

    @stack('scripts')
</body>
</html>
