<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('contacts', function (Blueprint $table) {
            $table->id();
            $table->string('name'); // ชื่อผู้ติดต่อ
            $table->string('phone')->nullable(); // เบอร์โทร
            $table->string('email')->nullable(); // อีเมล
            $table->text('address')->nullable(); // ที่อยู่
            $table->text('message')->nullable(); // ข้อความ
            $table->boolean('is_read')->default(false); // สถานะการอ่าน
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('contacts');
    }
};
