/**
 * Admin Activities JavaScript
 * Enhanced functionality for activity management
 */

class ActivityManager {
    constructor() {
        this.init();
    }

    init() {
        this.setupEventListeners();
        this.setupImagePreviews();
        this.setupFormValidation();
        this.setupTooltips();
    }

    setupEventListeners() {
        // File input change events
        document.addEventListener('change', (e) => {
            if (e.target.classList.contains('custom-file-input')) {
                this.updateFileLabel(e.target);
            }
            
            if (e.target.classList.contains('gallery-image') || e.target.id === 'cover_image') {
                this.handleImagePreview(e.target);
            }
        });

        // Button click events
        document.addEventListener('click', (e) => {
            if (e.target.closest('.remove-gallery-item')) {
                this.removeGalleryItem(e.target);
            }
            
            if (e.target.closest('#add_gallery_image')) {
                this.addGalleryItem();
            }
            
            if (e.target.closest('.delete-image')) {
                this.deleteExistingImage(e.target);
            }
        });

        // Form submission
        document.addEventListener('submit', (e) => {
            if (e.target.closest('form[enctype="multipart/form-data"]')) {
                this.handleFormSubmission(e);
            }
        });
    }

    setupImagePreviews() {
        // Setup drag and drop for image uploads
        const dropZones = document.querySelectorAll('.custom-file');
        
        dropZones.forEach(zone => {
            zone.addEventListener('dragover', (e) => {
                e.preventDefault();
                zone.classList.add('drag-over');
            });
            
            zone.addEventListener('dragleave', () => {
                zone.classList.remove('drag-over');
            });
            
            zone.addEventListener('drop', (e) => {
                e.preventDefault();
                zone.classList.remove('drag-over');
                
                const files = e.dataTransfer.files;
                if (files.length > 0) {
                    const input = zone.querySelector('input[type="file"]');
                    input.files = files;
                    this.updateFileLabel(input);
                    this.handleImagePreview(input);
                }
            });
        });
    }

    setupFormValidation() {
        const forms = document.querySelectorAll('form');
        
        forms.forEach(form => {
            const inputs = form.querySelectorAll('input[required], textarea[required], select[required]');
            
            inputs.forEach(input => {
                input.addEventListener('blur', () => {
                    this.validateField(input);
                });
                
                input.addEventListener('input', () => {
                    if (input.classList.contains('is-invalid')) {
                        this.validateField(input);
                    }
                });
            });
        });
    }

    setupTooltips() {
        // Initialize Bootstrap tooltips if available
        if (typeof bootstrap !== 'undefined' && bootstrap.Tooltip) {
            const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
            tooltipTriggerList.map(function (tooltipTriggerEl) {
                return new bootstrap.Tooltip(tooltipTriggerEl);
            });
        }
    }

    updateFileLabel(input) {
        const label = input.nextElementSibling;
        const fileName = input.files[0] ? input.files[0].name : 'เลือกรูปภาพ...';
        
        if (label && label.classList.contains('custom-file-label')) {
            label.textContent = fileName;
            
            // Add animation
            label.style.transform = 'scale(0.95)';
            setTimeout(() => {
                label.style.transform = 'scale(1)';
            }, 100);
        }
    }

    handleImagePreview(input) {
        const file = input.files[0];
        if (!file) return;

        // Validate file type
        if (!this.isValidImageFile(file)) {
            this.showError(input, 'กรุณาเลือกไฟล์รูปภาพที่ถูกต้อง (JPEG, JPG, PNG, GIF, WebP)');
            input.value = '';
            return;
        }

        // Validate file size (2MB)
        if (file.size > 2 * 1024 * 1024) {
            this.showError(input, 'ขนาดไฟล์ต้องไม่เกิน 2MB');
            input.value = '';
            return;
        }

        // Show preview
        this.showImagePreview(input, file);
        this.clearError(input);
    }

    isValidImageFile(file) {
        const validTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'];
        return validTypes.includes(file.type);
    }

    showImagePreview(input, file) {
        const reader = new FileReader();
        
        reader.onload = (e) => {
            let preview;
            
            if (input.id === 'cover_image') {
                preview = document.getElementById('cover_preview');
                const img = document.getElementById('cover_preview_img');
                if (img) {
                    img.src = e.target.result;
                    preview.style.display = 'block';
                    preview.classList.add('fade-in');
                }
            } else if (input.classList.contains('gallery-image')) {
                preview = input.closest('.gallery-item').querySelector('.image-preview');
                const img = preview.querySelector('img');
                if (img) {
                    img.src = e.target.result;
                    preview.style.display = 'block';
                    preview.classList.add('fade-in');
                }
            }
        };
        
        reader.readAsDataURL(file);
    }

    addGalleryItem() {
        const container = document.getElementById('gallery_container');
        const newItem = document.createElement('div');
        newItem.className = 'gallery-item mb-3 fade-in';
        
        newItem.innerHTML = `
            <div class="row">
                <div class="col-md-8">
                    <div class="custom-file">
                        <input type="file"
                               name="gallery_images[]"
                               class="custom-file-input gallery-image"
                               accept="image/*">
                        <label class="custom-file-label">เลือกรูปภาพ...</label>
                    </div>
                </div>
                <div class="col-md-3">
                    <input type="text"
                           name="captions[]"
                           class="form-control"
                           placeholder="คำบรรยายรูปภาพ (ไม่บังคับ)">
                </div>
                <div class="col-md-1">
                    <button type="button" class="btn btn-danger btn-sm remove-gallery-item" title="ลบรูปภาพ">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            </div>
            <div class="image-preview mt-2" style="display: none;">
                <img src="" class="img-thumbnail" style="max-width: 150px;">
            </div>
        `;
        
        container.appendChild(newItem);
        
        // Animate the new item
        setTimeout(() => {
            newItem.scrollIntoView({ behavior: 'smooth', block: 'nearest' });
        }, 100);
    }

    removeGalleryItem(button) {
        const item = button.closest('.gallery-item');
        
        // Add slide-out animation
        item.style.transform = 'translateX(100%)';
        item.style.opacity = '0';
        
        setTimeout(() => {
            item.remove();
        }, 300);
    }

    deleteExistingImage(button) {
        const imageId = button.getAttribute('data-image-id');
        
        if (!confirm('คุณแน่ใจหรือไม่ที่จะลบรูปภาพนี้?')) {
            return;
        }

        // Show loading state
        button.classList.add('loading');
        button.disabled = true;

        // Get CSRF token
        const token = document.querySelector('meta[name="csrf-token"]')?.getAttribute('content');
        
        if (!token) {
            this.showAlert('ไม่พบ CSRF token', 'error');
            return;
        }

        // Make delete request
        fetch(`/admin/activities/images/${imageId}`, {
            method: 'DELETE',
            headers: {
                'X-CSRF-TOKEN': token,
                'Content-Type': 'application/json',
            },
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                const container = button.closest('.gallery-item-container');
                container.style.transform = 'scale(0)';
                container.style.opacity = '0';
                
                setTimeout(() => {
                    container.remove();
                    this.showAlert('ลบรูปภาพสำเร็จ', 'success');
                }, 300);
            } else {
                this.showAlert('เกิดข้อผิดพลาดในการลบรูปภาพ', 'error');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            this.showAlert('เกิดข้อผิดพลาดในการลบรูปภาพ', 'error');
        })
        .finally(() => {
            button.classList.remove('loading');
            button.disabled = false;
        });
    }

    validateField(field) {
        const value = field.value.trim();
        let isValid = true;
        let message = '';

        if (field.hasAttribute('required') && !value) {
            isValid = false;
            message = 'กรุณากรอกข้อมูลในช่องนี้';
        }

        if (field.type === 'email' && value && !this.isValidEmail(value)) {
            isValid = false;
            message = 'กรุณากรอกอีเมลที่ถูกต้อง';
        }

        if (isValid) {
            this.clearError(field);
        } else {
            this.showError(field, message);
        }

        return isValid;
    }

    isValidEmail(email) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return emailRegex.test(email);
    }

    showError(field, message) {
        field.classList.add('is-invalid');
        field.classList.remove('is-valid');
        
        let feedback = field.parentNode.querySelector('.invalid-feedback');
        if (!feedback) {
            feedback = document.createElement('div');
            feedback.className = 'invalid-feedback';
            field.parentNode.appendChild(feedback);
        }
        feedback.textContent = message;
    }

    clearError(field) {
        field.classList.remove('is-invalid');
        field.classList.add('is-valid');
        
        const feedback = field.parentNode.querySelector('.invalid-feedback');
        if (feedback) {
            feedback.remove();
        }
    }

    handleFormSubmission(e) {
        const form = e.target;
        const submitBtn = form.querySelector('button[type="submit"]');
        
        if (submitBtn) {
            submitBtn.classList.add('loading');
            submitBtn.disabled = true;
            
            // Re-enable after 5 seconds as fallback
            setTimeout(() => {
                submitBtn.classList.remove('loading');
                submitBtn.disabled = false;
            }, 5000);
        }
    }

    showAlert(message, type = 'info') {
        const alertDiv = document.createElement('div');
        alertDiv.className = `alert alert-${type === 'error' ? 'danger' : type} alert-dismissible fade show`;
        alertDiv.innerHTML = `
            ${message}
            <button type="button" class="close" data-dismiss="alert">
                <span>&times;</span>
            </button>
        `;
        
        // Insert at the top of the content
        const content = document.querySelector('.content-wrapper .content');
        if (content) {
            content.insertBefore(alertDiv, content.firstChild);
            
            // Auto-dismiss after 5 seconds
            setTimeout(() => {
                alertDiv.remove();
            }, 5000);
        }
    }
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    new ActivityManager();
});

// Export for use in other scripts
window.ActivityManager = ActivityManager;
