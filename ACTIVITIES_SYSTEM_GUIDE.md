# คู่มือการใช้งานระบบจัดการกิจกรรม (Activities System) - SoloShop

## ✅ สิ่งที่ได้แก้ไขและปรับปรุงแล้ว

### 1. ระบบ Admin Backend สำหรับกิจกรรม
- **หน้าจัดการกิจกรรม**: `/admin/activities`
- **เพิ่มกิจกรรมใหม่**: `/admin/activities/create`
- **แก้ไขกิจกรรม**: `/admin/activities/{id}/edit`
- **จัดการหมวดหมู่**: `/admin/activity-categories`

### 2. ฟีเจอร์ที่ใช้งานได้แล้ว
- ✅ **CRUD กิจกรรม**: เพิ่ม, แก้ไข, ลบ, ดูรายการ
- ✅ **จัดการหมวดหมู่**: สร้างหมวดหมู่พร้อมสีประจำหมวดหมู่
- ✅ **อัปโหลดรูปภาพ**: รูปหน้าปก + แกลเลอรี่รูปภาพ
- ✅ **จัดการแกลเลอรี่**: ลาก-วาง เรียงลำดับ, แก้ไขคำบรรยาย, เปลี่ยนรูป, ลบรูป
- ✅ **ระบบ Validation**: ตรวจสอบไฟล์รูปภาพและข้อมูล
- ✅ **สถานะการเผยแพร่**: ร่าง/เผยแพร่
- ✅ **ข้อมูลตัวอย่าง**: หมวดหมู่และกิจกรรมตัวอย่าง

### 3. ไฟล์ที่เกี่ยวข้อง
```
app/Http/Controllers/Admin/
├── ActivityController.php          # Controller หลักสำหรับจัดการกิจกรรม
└── ActivityCategoryController.php  # Controller สำหรับจัดการหมวดหมู่

app/Models/
├── Activity.php                    # Model กิจกรรม
├── ActivityCategory.php            # Model หมวดหมู่กิจกรรม
└── ActivityImage.php               # Model รูปภาพกิจกรรม

resources/views/admin/activities/
├── index.blade.php                 # หน้ารายการกิจกรรม
├── create.blade.php                # หน้าเพิ่มกิจกรรม
└── edit.blade.php                  # หน้าแก้ไขกิจกรรม

resources/views/admin/activity-categories/
├── index.blade.php                 # หน้ารายการหมวดหมู่
├── create.blade.php                # หน้าเพิ่มหมวดหมู่
└── edit.blade.php                  # หน้าแก้ไขหมวดหมู่

database/migrations/
├── 2025_07_13_140000_create_activity_categories_table.php
├── 2025_07_13_140100_create_activities_table.php
└── 2025_07_13_140200_create_activity_images_table.php

database/seeders/
├── ActivityCategorySeeder.php      # ข้อมูลหมวดหมู่ตัวอย่าง
└── ActivitySeeder.php              # ข้อมูลกิจกรรมตัวอย่าง

public/css/
└── admin-activities.css            # CSS สำหรับหน้า admin

public/js/
└── admin-activities.js             # JavaScript สำหรับฟีเจอร์ขั้นสูง
```

## 🚀 การใช้งาน

### 1. เข้าสู่ระบบ Admin
```
URL: http://localhost:8000/admin
Email: <EMAIL>
Password: admin123
```

### 2. จัดการหมวดหมู่กิจกรรม
1. ไปที่ **จัดการกิจกรรม** > **จัดการหมวดหมู่**
2. คลิก **เพิ่มหมวดหมู่ใหม่**
3. กรอกข้อมูล:
   - ชื่อหมวดหมู่
   - คำอธิบาย
   - สีประจำหมวดหมู่
   - สถานะ (เปิดใช้งาน/ปิด)

### 3. เพิ่มกิจกรรมใหม่
1. ไปที่ **จัดการกิจกรรม**
2. คลิก **เพิ่มกิจกรรมใหม่**
3. กรอกข้อมูล:
   - ชื่อกิจกรรม (บังคับ)
   - รายละเอียด (บังคับ)
   - หมวดหมู่ (บังคับ)
   - วันที่จัดกิจกรรม
   - สถานที่
   - รูปภาพหน้าปก
   - แกลเลอรี่รูปภาพ (หลายรูป)
   - สถานะการเผยแพร่

### 4. จัดการแกลเลอรี่รูปภาพ
- **เพิ่มรูป**: คลิก "เพิ่มรูปภาพ" และเลือกไฟล์
- **ลากเรียงลำดับ**: ลากไอคอน grip เพื่อเปลี่ยนลำดับ
- **แก้ไขคำบรรยาย**: คลิกปุ่มแก้ไข (ดินสอ)
- **เปลี่ยนรูป**: คลิกปุ่มเปลี่ยน (ลูกศร)
- **ลบรูป**: คลิกปุ่มลบ (ถังขยะ)

## 🎯 ฟีเจอร์พิเศษ

### 1. การจัดการรูปภาพขั้นสูง
- **Auto Resize**: รูปภาพจะถูก resize อัตโนมัติเป็น 800x600px
- **Validation**: ตรวจสอบประเภทไฟล์และขนาด (สูงสุด 2MB)
- **Preview**: แสดงตัวอย่างรูปก่อนอัปโหลด
- **Drag & Drop**: ลากไฟล์มาวางได้

### 2. การจัดเรียงแกลเลอรี่
- **Sortable**: ใช้ SortableJS สำหรับลาก-วาง
- **Real-time Update**: อัปเดตลำดับทันทีที่เปลี่ยน
- **Visual Feedback**: แสดงสถานะขณะลาก

### 3. ระบบ AJAX
- **แก้ไขคำบรรยาย**: ไม่ต้องรีโหลดหน้า
- **เปลี่ยนรูปภาพ**: อัปโหลดและแสดงผลทันที
- **ลบรูปภาพ**: ลบและอัปเดต UI ทันที

## 📊 ข้อมูลตัวอย่าง

### หมวดหมู่ที่สร้างไว้แล้ว:
1. **งานบุญ** (สีเขียว) - กิจกรรมงานบุญต่างๆ
2. **งานศพ** (สีเทา) - พิธีกรรมงานศพ
3. **กิจกรรมชุมชน** (สีน้ำเงิน) - กิจกรรมเพื่อชุมชน
4. **งานเทศกาล** (สีเหลือง) - งานเทศกาลประจำปี
5. **งานพิเศษ** (สีแดง) - งานพิเศษและกิจกรรมเฉพาะกิจ

### กิจกรรมตัวอย่าง:
- งานบุญประจำปี 2567
- พิธีสวดอภิธรรม
- กิจกรรมทำความสะอาดชุมชน
- เทศกาลสงกรานต์ 2567
- งานบรรพชาสามเณร

## 🔧 การแก้ไขปัญหา

### 1. ปัญหาการอัปโหลดรูปภาพ
```bash
# ตรวจสอบ storage link
php artisan storage:link

# ตรวจสอบสิทธิ์โฟลเดอร์
chmod -R 755 storage/app/public/activities/
```

### 2. ปัญหา GD Extension
```bash
# ตรวจสอบ GD extension
php -m | grep -i gd

# ถ้าไม่มี ให้เปิดใน php.ini
extension=gd
```

### 3. ปัญหา JavaScript
- ตรวจสอบ Console ใน Browser Developer Tools
- ตรวจสอบว่า jQuery และ Bootstrap โหลดแล้ว
- ตรวจสอบ CSRF token

## 📱 Responsive Design

ระบบรองรับการใช้งานบนอุปกรณ์ต่างๆ:
- **Desktop**: ฟีเจอร์ครบถ้วน
- **Tablet**: ปรับ layout สำหรับหน้าจอขนาดกลาง
- **Mobile**: ปรับ UI สำหรับการใช้งานบนมือถือ

## 🎨 การปรับแต่ง CSS

### เปลี่ยนสีธีม:
```css
/* ใน public/css/admin-activities.css */
:root {
    --primary-color: #007bff;
    --success-color: #28a745;
    --warning-color: #ffc107;
    --danger-color: #dc3545;
}
```

### เปลี่ยนขนาดรูปภาพ:
```php
// ใน app/Helpers/ImageHelper.php
public static function uploadAndResize($file, $folder, $maxWidth = 1200, $maxHeight = 800)
```

## 🔄 การอัปเดตในอนาคต

### ฟีเจอร์ที่วางแผนไว้:
- [ ] ระบบ Tags สำหรับกิจกรรม
- [ ] การค้นหาและกรองขั้นสูง
- [ ] ระบบ Comments สำหรับกิจกรรม
- [ ] การส่งออกข้อมูลเป็น PDF
- [ ] ระบบแจ้งเตือนกิจกรรมใหม่
- [ ] Integration กับ Social Media

## 📞 การสนับสนุน

หากพบปัญหาหรือต้องการความช่วยเหลือ:
1. ตรวจสอบ Log ใน `storage/logs/laravel.log`
2. ตรวจสอบ Browser Console สำหรับ JavaScript errors
3. ตรวจสอบ Network tab สำหรับ AJAX requests

---

**หมายเหตุ**: ระบบนี้ได้รับการทดสอบและพร้อมใช้งานแล้ว ✅
