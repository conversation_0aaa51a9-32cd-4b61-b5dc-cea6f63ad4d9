

<?php $__env->startSection('title', 'เข้าสู่ระบบ - SoloShop'); ?>

<?php $__env->startSection('content'); ?>
<form method="POST" action="<?php echo e(route('login')); ?>" id="loginForm">
    <?php echo csrf_field(); ?>

    <?php if($errors->any()): ?>
        <div class="alert alert-danger">
            <i class="fas fa-exclamation-triangle me-2"></i>
            ข้อมูลไม่ถูกต้อง กรุณาลองใหม่อีกครั้ง
        </div>
    <?php endif; ?>

    <div class="form-group">
        <input id="email"
               type="email"
               class="form-control <?php $__errorArgs = ['email'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
               name="email"
               value="<?php echo e(old('email')); ?>"
               required
               autocomplete="email"
               autofocus
               placeholder="อีเมล">
        <?php $__errorArgs = ['email'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
            <div class="invalid-feedback d-block">
                <?php echo e($message); ?>

            </div>
        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
    </div>

    <div class="form-group">
        <input id="password"
               type="password"
               class="form-control <?php $__errorArgs = ['password'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
               name="password"
               required
               autocomplete="current-password"
               placeholder="รหัสผ่าน">
        <?php $__errorArgs = ['password'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
            <div class="invalid-feedback d-block">
                <?php echo e($message); ?>

            </div>
        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
    </div>

    <div class="d-grid mb-3">
        <button type="submit" class="btn btn-primary" id="loginBtn">
            ลงชื่อเข้าใช้
        </button>
    </div>

    <div class="text-center mb-3">
        <small class="text-muted">ยังไม่ได้เป็นสมาชิก? ลงทะเบียนเลย</small>
    </div>
</form>

    <div class="test-credentials" onclick="fillDemoCredentials()">
        <div class="text-center mb-2">
            <span class="badge">ข้อมูลทดสอบ</span>
        </div>
        <div class="row text-center">
            <div class="col-6">
                <small class="text-muted d-block">อีเมล</small>
                <small class="fw-bold text-primary"><EMAIL></small>
            </div>
            <div class="col-6">
                <small class="text-muted d-block">รหัสผ่าน</small>
                <small class="fw-bold text-primary">admin123</small>
            </div>
        </div>
        <div class="mt-2 text-center">
            <small class="text-muted">คลิกเพื่อเติมข้อมูลอัตโนมัติ</small>
        </div>
    </div>

<?php $__env->startPush('scripts'); ?>
<script>
function togglePassword() {
    const passwordInput = document.getElementById('password');
    const passwordIcon = document.getElementById('passwordIcon');

    if (passwordInput.type === 'password') {
        passwordInput.type = 'text';
        passwordIcon.classList.remove('fa-eye');
        passwordIcon.classList.add('fa-eye-slash');
    } else {
        passwordInput.type = 'password';
        passwordIcon.classList.remove('fa-eye-slash');
        passwordIcon.classList.add('fa-eye');
    }
}

function fillDemoCredentials() {
    const emailInput = document.getElementById('email');
    const passwordInput = document.getElementById('password');

    // Add gentle animation
    emailInput.style.transform = 'scale(1.02)';
    passwordInput.style.transform = 'scale(1.02)';

    setTimeout(() => {
        emailInput.value = '<EMAIL>';
        passwordInput.value = 'admin123';

        // Trigger floating label animation
        emailInput.dispatchEvent(new Event('input'));
        passwordInput.dispatchEvent(new Event('input'));

        // Reset scale
        emailInput.style.transform = 'scale(1)';
        passwordInput.style.transform = 'scale(1)';

        // Focus on submit button with delay
        setTimeout(() => {
            document.getElementById('loginBtn').focus();
        }, 300);
    }, 150);
}

document.getElementById('loginForm').addEventListener('submit', function() {
    const loginBtn = document.getElementById('loginBtn');
    loginBtn.classList.add('btn-loading');
    loginBtn.disabled = true;

    // Add gentle shake animation if there are errors
    setTimeout(() => {
        if (document.querySelector('.alert-danger')) {
            loginBtn.classList.remove('btn-loading');
            loginBtn.disabled = false;
        }
    }, 2000);
});

// Enhanced page interactions
document.addEventListener('DOMContentLoaded', function() {
    // Add subtle hover effects to form elements
    const formControls = document.querySelectorAll('.form-control');
    formControls.forEach(input => {
        input.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-1px)';
        });

        input.addEventListener('mouseleave', function() {
            if (this !== document.activeElement) {
                this.style.transform = 'translateY(0)';
            }
        });
    });
});
</script>
<?php $__env->stopPush(); ?>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.auth', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH D:\XAMPP\htdocs\SoloShop\resources\views/auth/login.blade.php ENDPATH**/ ?>