@extends('layouts.app')

@section('title', 'เพิ่มกิจกรรมใหม่')

@push('styles')
<link rel="stylesheet" href="{{ asset('css/admin-activities.css') }}">
@endpush

@section('content')
<div class="content-wrapper">
    <div class="content-header">
        <div class="container-fluid">
            <div class="row mb-2">
                <div class="col-sm-6">
                    <h1 class="m-0">เพิ่มกิจกรรมใหม่</h1>
                </div>
                <div class="col-sm-6">
                    <ol class="breadcrumb float-sm-right">
                        <li class="breadcrumb-item"><a href="{{ route('admin.dashboard') }}">หน้าแรก</a></li>
                        <li class="breadcrumb-item"><a href="{{ route('admin.activities.index') }}">จัดการกิจกรรม</a></li>
                        <li class="breadcrumb-item active">เพิ่มกิจกรรมใหม่</li>
                    </ol>
                </div>
            </div>
        </div>
    </div>

    <section class="content">
        <div class="container-fluid">
            <div class="row">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">ข้อมูลกิจกรรม</h3>
                        </div>
                        <form action="{{ route('admin.activities.store') }}" method="POST" enctype="multipart/form-data">
                            @csrf
                            <div class="card-body">
                                @if($errors->any())
                                    <div class="alert alert-danger">
                                        <ul class="mb-0">
                                            @foreach($errors->all() as $error)
                                                <li>{{ $error }}</li>
                                            @endforeach
                                        </ul>
                                    </div>
                                @endif

                                <div class="row">
                                    <div class="col-md-8">
                                        <div class="form-group">
                                            <label for="title">ชื่อกิจกรรม <span class="text-danger">*</span></label>
                                            <input type="text"
                                                   name="title"
                                                   id="title"
                                                   class="form-control @error('title') is-invalid @enderror"
                                                   value="{{ old('title') }}"
                                                   placeholder="เช่น งานบุญประจำปี 2567"
                                                   required>
                                            @error('title')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>

                                        <div class="form-group">
                                            <label for="description">รายละเอียดกิจกรรม <span class="text-danger">*</span></label>
                                            <textarea name="description"
                                                      id="description"
                                                      class="form-control @error('description') is-invalid @enderror"
                                                      rows="6"
                                                      placeholder="อธิบายรายละเอียดของกิจกรรม..."
                                                      required>{{ old('description') }}</textarea>
                                            @error('description')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>

                                        <div class="row">
                                            <div class="col-md-6">
                                                <div class="form-group">
                                                    <label for="activity_date">วันที่จัดกิจกรรม</label>
                                                    <input type="date"
                                                           name="activity_date"
                                                           id="activity_date"
                                                           class="form-control @error('activity_date') is-invalid @enderror"
                                                           value="{{ old('activity_date') }}">
                                                    @error('activity_date')
                                                        <div class="invalid-feedback">{{ $message }}</div>
                                                    @enderror
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="form-group">
                                                    <label for="location">สถานที่</label>
                                                    <input type="text"
                                                           name="location"
                                                           id="location"
                                                           class="form-control @error('location') is-invalid @enderror"
                                                           value="{{ old('location') }}"
                                                           placeholder="เช่น วัดพระแก้ว">
                                                    @error('location')
                                                        <div class="invalid-feedback">{{ $message }}</div>
                                                    @enderror
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="col-md-4">
                                        <div class="form-group">
                                            <label for="category_id">หมวดหมู่ <span class="text-danger">*</span></label>
                                            <select name="category_id"
                                                    id="category_id"
                                                    class="form-control @error('category_id') is-invalid @enderror"
                                                    required>
                                                <option value="">เลือกหมวดหมู่</option>
                                                @foreach($categories as $category)
                                                    <option value="{{ $category->id }}" 
                                                            {{ old('category_id') == $category->id ? 'selected' : '' }}>
                                                        {{ $category->name }}
                                                    </option>
                                                @endforeach
                                            </select>
                                            @error('category_id')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                            <small class="form-text text-muted">
                                                <a href="{{ route('admin.activity-categories.index') }}" target="_blank">
                                                    จัดการหมวดหมู่
                                                </a>
                                            </small>
                                        </div>

                                        <div class="form-group">
                                            <div class="custom-control custom-switch">
                                                <input type="checkbox" 
                                                       class="custom-control-input" 
                                                       id="is_published" 
                                                       name="is_published"
                                                       {{ old('is_published', true) ? 'checked' : '' }}>
                                                <label class="custom-control-label" for="is_published">เผยแพร่กิจกรรม</label>
                                            </div>
                                            <small class="form-text text-muted">
                                                หากไม่เลือก กิจกรรมจะถูกบันทึกเป็นร่าง
                                            </small>
                                        </div>
                                    </div>
                                </div>

                                <hr>

                                <!-- Cover Image Section -->
                                <div class="form-group">
                                    <label for="cover_image">รูปภาพหน้าปก</label>
                                    <div class="custom-file">
                                        <input type="file"
                                               name="cover_image"
                                               id="cover_image"
                                               class="custom-file-input @error('cover_image') is-invalid @enderror"
                                               accept="image/*">
                                        <label class="custom-file-label" for="cover_image">เลือกรูปภาพหน้าปก...</label>
                                        @error('cover_image')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                    <small class="form-text text-muted">
                                        รองรับไฟล์: JPEG, JPG, PNG, GIF, WebP (ขนาดไม่เกิน 2MB)
                                    </small>
                                    <div id="cover_preview" class="mt-2" style="display: none;">
                                        <img id="cover_preview_img" src="" class="img-thumbnail" style="max-width: 200px;">
                                    </div>
                                </div>

                                <hr>

                                <!-- Gallery Images Section -->
                                <div class="form-group">
                                    <label>แกลเลอรี่รูปภาพ</label>
                                    <div class="alert alert-info">
                                        <i class="fas fa-info-circle"></i>
                                        คุณสามารถเพิ่มรูปภาพหลายรูปพร้อมคำบรรยาย และดูตัวอย่างก่อนบันทึก
                                    </div>
                                    <div id="gallery_container">
                                        <div class="gallery-item mb-3 border rounded p-3">
                                            <div class="row">
                                                <div class="col-md-7">
                                                    <div class="custom-file">
                                                        <input type="file"
                                                               name="gallery_images[]"
                                                               class="custom-file-input gallery-image"
                                                               accept="image/*">
                                                        <label class="custom-file-label">เลือกรูปภาพ...</label>
                                                    </div>
                                                </div>
                                                <div class="col-md-5">
                                                    <input type="text"
                                                           name="captions[]"
                                                           class="form-control"
                                                           placeholder="คำบรรยายรูปภาพ (ไม่บังคับ)">
                                                </div>
                                            </div>
                                            <div class="image-preview mt-3" style="display: none;">
                                                <div class="d-flex align-items-center">
                                                    <img src="" class="img-thumbnail me-3" style="max-width: 120px; max-height: 120px;">
                                                    <div class="flex-grow-1">
                                                        <h6 class="mb-1">ตัวอย่างรูปภาพ</h6>
                                                        <p class="text-muted small mb-0">รูปภาพจะถูกปรับขนาดอัตโนมัติให้เหมาะสม</p>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="d-flex justify-content-between align-items-center">
                                        <button type="button" id="add_gallery_image" class="btn btn-outline-primary btn-sm">
                                            <i class="fas fa-plus"></i> เพิ่มรูปภาพอีก
                                        </button>
                                        <small class="text-muted">
                                            รองรับไฟล์: JPEG, JPG, PNG, GIF, WebP (ขนาดไม่เกิน 2MB ต่อรูป)
                                        </small>
                                    </div>
                                </div>
                            </div>
                            <div class="card-footer">
                                <button type="submit" class="btn btn-success">
                                    <i class="fas fa-save me-1"></i>บันทึกกิจกรรม
                                </button>
                                <a href="{{ route('admin.activities.index') }}" class="btn btn-secondary">
                                    <i class="fas fa-arrow-left me-1"></i>ย้อนกลับ
                                </a>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </section>
</div>

@push('styles')
<style>
.gallery-item {
    transition: all 0.3s ease;
    background-color: #f8f9fa;
}

.gallery-item:hover {
    background-color: #e9ecef;
    border-color: #007bff !important;
}

.image-preview img {
    transition: transform 0.2s ease;
}

.image-preview img:hover {
    transform: scale(1.05);
}

.custom-file-label {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}
</style>
@endpush

@push('scripts')
<script src="{{ asset('js/admin-activities.js') }}"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Handle cover image preview
    document.getElementById('cover_image').addEventListener('change', function(e) {
        const file = e.target.files[0];
        const preview = document.getElementById('cover_preview');
        const previewImg = document.getElementById('cover_preview_img');
        
        if (file) {
            const reader = new FileReader();
            reader.onload = function(e) {
                previewImg.src = e.target.result;
                preview.style.display = 'block';
            };
            reader.readAsDataURL(file);
        } else {
            preview.style.display = 'none';
        }
    });

    // Handle gallery images
    let galleryIndex = 1;
    
    document.getElementById('add_gallery_image').addEventListener('click', function() {
        const container = document.getElementById('gallery_container');
        const newItem = document.createElement('div');
        newItem.className = 'gallery-item mb-3 border rounded p-3';
        newItem.innerHTML = `
            <div class="row">
                <div class="col-md-6">
                    <div class="custom-file">
                        <input type="file"
                               name="gallery_images[]"
                               class="custom-file-input gallery-image"
                               accept="image/*">
                        <label class="custom-file-label">เลือกรูปภาพ...</label>
                    </div>
                </div>
                <div class="col-md-5">
                    <input type="text"
                           name="captions[]"
                           class="form-control"
                           placeholder="คำบรรยายรูปภาพ (ไม่บังคับ)">
                </div>
                <div class="col-md-1">
                    <button type="button" class="btn btn-outline-danger btn-sm remove-gallery-item" title="ลบรูปภาพนี้">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            </div>
            <div class="image-preview mt-3" style="display: none;">
                <div class="d-flex align-items-center">
                    <img src="" class="img-thumbnail me-3" style="max-width: 120px; max-height: 120px;">
                    <div class="flex-grow-1">
                        <h6 class="mb-1">ตัวอย่างรูปภาพ</h6>
                        <p class="text-muted small mb-0">รูปภาพจะถูกปรับขนาดอัตโนมัติให้เหมาะสม</p>
                    </div>
                </div>
            </div>
        `;
        container.appendChild(newItem);
        galleryIndex++;
    });

    // Handle gallery image preview and removal
    document.addEventListener('change', function(e) {
        if (e.target.classList.contains('gallery-image')) {
            const file = e.target.files[0];
            const preview = e.target.closest('.gallery-item').querySelector('.image-preview');
            const previewImg = preview.querySelector('img');
            
            if (file) {
                const reader = new FileReader();
                reader.onload = function(e) {
                    previewImg.src = e.target.result;
                    preview.style.display = 'block';
                };
                reader.readAsDataURL(file);
            } else {
                preview.style.display = 'none';
            }
        }
    });

    document.addEventListener('click', function(e) {
        if (e.target.classList.contains('remove-gallery-item') || e.target.closest('.remove-gallery-item')) {
            e.target.closest('.gallery-item').remove();
        }
    });

    // Update file input labels
    document.addEventListener('change', function(e) {
        if (e.target.classList.contains('custom-file-input')) {
            const fileName = e.target.files[0] ? e.target.files[0].name : 'เลือกรูปภาพ...';
            const label = e.target.nextElementSibling;
            label.textContent = fileName;
        }
    });
});
</script>
@endpush
@endsection
