<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;

class CheckFileUpload
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure(\Illuminate\Http\Request): (\Illuminate\Http\Response|\Illuminate\Http\RedirectResponse)  $next
     * @return \Illuminate\Http\Response|\Illuminate\Http\RedirectResponse
     */
    public function handle(Request $request, Closure $next)
    {
        $errors = [];

        // Check single image upload
        if ($request->hasFile('image')) {
            $errors = array_merge($errors, $this->validateSingleFile($request->file('image'), 'image'));
        }

        // Check cover image upload
        if ($request->hasFile('cover_image')) {
            $errors = array_merge($errors, $this->validateSingleFile($request->file('cover_image'), 'cover_image'));
        }

        // Check gallery images upload
        if ($request->hasFile('gallery_images')) {
            foreach ($request->file('gallery_images') as $index => $file) {
                if ($file) {
                    $fieldErrors = $this->validateSingleFile($file, "gallery_images.{$index}");
                    $errors = array_merge($errors, $fieldErrors);
                }
            }
        }

        // Check new_image upload (for image replacement)
        if ($request->hasFile('new_image')) {
            $errors = array_merge($errors, $this->validateSingleFile($request->file('new_image'), 'new_image'));
        }

        if (!empty($errors)) {
            return back()->withErrors($errors)->withInput();
        }

        return $next($request);
    }

    /**
     * Validate a single file upload
     */
    private function validateSingleFile($file, $fieldName)
    {
        $errors = [];

        // Check if upload was successful
        if (!$file->isValid()) {
            $errors[$fieldName] = 'การอัปโหลดไฟล์ล้มเหลว กรุณาลองใหม่อีกครั้ง';
            return $errors;
        }

        // Check file size (2MB = 2048KB)
        if ($file->getSize() > 2048 * 1024) {
            $errors[$fieldName] = 'ขนาดไฟล์ต้องไม่เกิน 2MB';
        }

        // Check file type
        $allowedTypes = ['jpeg', 'jpg', 'png', 'gif', 'webp'];
        $extension = strtolower($file->getClientOriginalExtension());

        if (!in_array($extension, $allowedTypes)) {
            $errors[$fieldName] = 'ไฟล์ต้องเป็นรูปภาพ (JPEG, JPG, PNG, GIF, WebP)';
        }

        // Check if it's actually an image
        try {
            $imageInfo = getimagesize($file->getPathname());
            if (!$imageInfo) {
                $errors[$fieldName] = 'ไฟล์ไม่ใช่รูปภาพที่ถูกต้อง';
            }
        } catch (\Exception $e) {
            $errors[$fieldName] = 'ไม่สามารถอ่านไฟล์รูปภาพได้';
        }

        return $errors;
    }
}
